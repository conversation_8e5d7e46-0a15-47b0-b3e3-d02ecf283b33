import { Email } from '@convex-dev/auth/providers/Email';
import { Resend as ResendAPI } from 'resend';
import { RandomReader, generateRandomString } from '@oslojs/crypto/random';
import { OtpTemplate } from '../src/features/email/components/otp-template';
import { generateUnsubscribeUrl } from '../src/lib/email-unsubscribe';

export const ResendOTP = Email({
	id: 'resend-otp',
	apiKey: process.env.AUTH_RESEND_KEY,
	maxAge: 60 * 15, // 15 minutes
	profile(params) {
		return {
			email: params.email as string,
			name: params.name as string,
		};
	},
	async generateVerificationToken() {
		const random: RandomReader = {
			read(bytes) {
				crypto.getRandomValues(bytes);
			},
		};

		const alphabet = '**********';
		const length = 8;
		return generateRandomString(random, alphabet, length);
	},
	async sendVerificationRequest({ identifier: email, provider, token, params }) {
		const resend = new ResendAPI(provider.apiKey);

		// Check if this is a signup or signin based on the flow parameter
		const isSignUp = params?.flow === 'signUp';
		const userName = params?.name || '';

		// Generate unsubscribe URL (optional - we don't have user ID at this point)
		// For OTP emails, we'll include a generic unsubscribe notice
		const unsubscribeUrl = undefined; // Could be enhanced later with user lookup

		const subject = isSignUp
			? `Welcome to Proddy! Your verification code: ${token}`
			: `Your Proddy sign-in code: ${token}`;

		const textContent = isSignUp
			? `Welcome to Proddy${userName ? `, ${userName}` : ''}!\n\nYour verification code is: ${token}\n\nEnter this 8-digit code in your browser to complete your account setup. This code will expire in 15 minutes.\n\nIf you didn't request this code, you can safely ignore this email.`
			: `Your Proddy sign-in code is: ${token}\n\nEnter this 8-digit code in your browser to complete the sign-in process. This code will expire in 15 minutes.\n\nIf you didn't request this code, you can safely ignore this email.`;

		const { error } = await resend.emails.send({
			from: 'Proddy <<EMAIL>>',
			to: [email],
			subject,
			react: OtpTemplate({
				code: token,
				email: email,
				unsubscribeUrl,
				isSignUp,
				userName,
			}),
			text: textContent,
		});

		if (error) {
			throw new Error(JSON.stringify(error));
		}
	},
});
