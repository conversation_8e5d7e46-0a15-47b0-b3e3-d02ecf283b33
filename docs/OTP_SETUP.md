# OTP Authentication Setup

This document explains how to set up OTP (One-Time Password) authentication using Resend in your Proddy application.

## Prerequisites

1. A Resend account and API key
2. Convex deployment configured
3. Environment variables properly set

## Environment Variables

You need to set the following environment variable in your Convex deployment:

```bash
# For OTP authentication
npx convex env set AUTH_RESEND_KEY your-resend-api-key
```

Or using bun:

```bash
bunx convex env set AUTH_RESEND_KEY your-resend-api-key
```

**Note:** This is separate from the `RESEND_API_KEY` used for other email functionality in the app.

## Getting Your Resend API Key

1. Go to [Resend](https://resend.com)
2. Sign up or log in to your account
3. Navigate to the API Keys section
4. Create a new API key
5. Copy the API key and use it in the environment variable above

## Domain Configuration

For production use, you'll want to:

1. Add your domain to Resend
2. Verify your domain
3. Update the `from` address in `convex/resendotp.ts` from `'Proddy <<EMAIL>>'` to your verified domain

## Features Implemented

### OTP Sign-In Component
- **Location**: `src/features/auth/components/otp-sign-in-card.tsx`
- **Features**:
  - Email input with validation
  - 8-digit OTP code input with auto-submit
  - Error handling and loading states
  - Back navigation options

### Email Template
- **Location**: `src/features/email/components/otp-template.tsx`
- **Features**:
  - Professional React email template
  - Responsive design
  - Unsubscribe link support
  - Branded with Proddy logo

### Integration Points
- Added to existing auth flow in `src/features/auth/components/auth-screen.tsx`
- Available in both modal and standalone page formats
- Standalone page at `/signin/otp`

## Usage

### In Auth Modal
Users can click "Continue with Email Code" from the sign-in or sign-up forms to switch to OTP authentication.

### Standalone Page
Users can navigate directly to `/signin/otp` for OTP-only authentication.

### Flow
1. User enters email address
2. System sends 8-digit code via email
3. User enters code to complete authentication
4. Code expires after 15 minutes

## Testing

To test the OTP functionality:

1. Ensure `AUTH_RESEND_KEY` is set in your Convex environment
2. Start your development server
3. Navigate to `/signin/otp` or use the auth modal
4. Enter a valid email address
5. Check your email for the OTP code
6. Enter the code to complete sign-in

## Troubleshooting

### Email Not Received
- Check your Resend dashboard for delivery status
- Verify the API key is correctly set
- Check spam/junk folders
- Ensure the email address is valid

### Code Not Working
- Codes expire after 15 minutes
- Codes are case-sensitive (numbers only)
- Each code can only be used once
- Check for typos in the 8-digit code

### Domain Issues
- For production, use a verified domain in Resend
- Update the `from` address in the ResendOTP provider
- Ensure SPF/DKIM records are properly configured

## Security Notes

- Codes are 8 digits long for security
- Codes expire after 15 minutes
- Rate limiting is automatically handled by the auth library
- Failed attempts are tracked and limited

## Customization

### Email Template
Modify `src/features/email/components/otp-template.tsx` to customize:
- Branding and styling
- Email content and messaging
- Unsubscribe links

### Code Generation
Modify `convex/resendotp.ts` to customize:
- Code length (currently 8 digits)
- Code expiration time (currently 15 minutes)
- Email sending logic

### UI Components
Modify `src/features/auth/components/otp-sign-in-card.tsx` to customize:
- Input styling and behavior
- Error messages
- Loading states
- Navigation flow
