@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import react-day-picker styles */
@import 'react-day-picker/style.css';

@layer base {
	:root {
		/* Colors */
		--background: 0 0% 100%;
		--foreground: 222.2 84% 4.9%;
		--card: 0 0% 100%;
		--card-foreground: 222.2 84% 4.9%;
		--popover: 0 0% 100%;
		--popover-foreground: 222.2 84% 4.9%;
		--primary: 280 77% 23%;
		--primary-foreground: 0 0% 100%;
		--secondary: 326 86% 50%;
		--secondary-foreground: 0 0% 100%;
		--muted: 210 40% 96.1%;
		--muted-foreground: 215.4 16.3% 46.9%;
		--accent: 210 40% 96.1%;
		--accent-foreground: 222.2 47.4% 11.2%;
		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 210 40% 98%;
		--border: 214.3 31.8% 91.4%;
		--input: 214.3 31.8% 91.4%;
		--ring: 326 86% 50%;
		--chart-1: 12 76% 61%;
		--chart-2: 173 58% 39%;
		--chart-3: 197 37% 24%;
		--chart-4: 43 74% 66%;
		--chart-5: 27 87% 67%;

		/* Border Radius */
		--radius: 0.625rem; /* 10px */

		/* Animation Durations */
		--duration-fast: 150ms;
		--duration-normal: 200ms;
		--duration-slow: 300ms;

		/* Shadows */
		--shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
		--shadow-md:
			0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
		--shadow-lg:
			0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
		--shadow-xl:
			0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
	}

	/* Enable smooth scrolling for the entire website */
	html {
		scroll-behavior: smooth;
	}
}

@layer base {
	* {
		@apply border-border;
	}

	body {
		@apply bg-background text-foreground;
	}

	html,
	body,
	:root {
		@apply h-full;
	}
}

.ql-toolbar {
	border: none !important;
	background-color: #f8f8f8 !important;
}

.ql-toolbar .ql-formats:not(:last-child) {
	border-right: 1px solid #dcdcdc !important;
	padding-right: 12px !important;
}

.ql-container {
	font-family: 'Poppins', sans-serif !important;
	border: none !important;
	height: unset !important;
}

.ql-editor {
	line-height: 22px !important;
	padding: 8px 12px !important;
	color: #1d1c1d !important;
	font-weight: 400 !important;
	font-family: 'Poppins', sans-serif !important;
	font-size: 14px !important;
}

.ql-editor a {
	color: #1264a3 !important;
}

.ql-editor a:hover {
	text-decoration: underline !important;
}

.ql-editor:before {
	left: 12px !important;
	font-family: 'Poppins', sans-serif !important;
	color: #8d8d8d !important;
	font-style: normal !important;
	font-size: 14px !important;
	font-weight: 400 !important;

	@apply !truncate;
}

.ql-renderer {
	@apply !p-0;
}

/* Calendar event highlight */
.calendar-event-highlight {
	background-color: rgba(var(--secondary), 0.2);
	color: hsl(var(--secondary));
	padding: 2px 4px;
	border-radius: 4px;
	font-weight: 600;
	border: 1px solid rgba(var(--secondary), 0.3);
	display: inline-block;
	margin: 0 2px;
}

/* Canvas cursor styles */
.cursor-default {
	cursor: default;
}

.cursor-pencil {
	cursor:
		url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='black' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><path d='M17 3a2.85 2.85 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z'></path></svg>")
			0 24,
		auto;
}

.cursor-text {
	cursor: text;
}

.cursor-note {
	cursor:
		url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='black' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><path d='M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z'></path><polyline points='14 2 14 8 20 8'></polyline><line x1='16' y1='13' x2='8' y2='13'></line><line x1='16' y1='17' x2='8' y2='17'></line><line x1='10' y1='9' x2='8' y2='9'></line></svg>")
			0 24,
		auto;
}

.cursor-crosshair {
	cursor: crosshair;
}

.cursor-move {
	cursor: move;
}

.cursor-nwse-resize {
	cursor: nwse-resize;
}

@layer utilities {
	.messages-scrollbar {
		scrollbar-width: thin;
		scrollbar-color: #dcdcdc #f8f8f8;
	}

	/* Animation Utilities */
	.animate-fade-in {
		animation: fadeIn var(--duration-normal) ease-in-out;
	}

	.animate-slide-up {
		animation: slideUp var(--duration-normal) ease-in-out;
	}

	.animate-slide-down {
		animation: slideDown var(--duration-normal) ease-in-out;
	}

	.animate-pulse-subtle {
		animation: pulseSlight 2s infinite;
	}

	.animate-float {
		animation: float 3s ease-in-out infinite;
	}

	/* Hover Effects */
	.hover-scale {
		transition: transform var(--duration-normal) ease-in-out;
	}

	.hover-scale:hover {
		transform: scale(1.05);
	}

	.hover-rotate {
		transition: transform var(--duration-normal) ease-in-out;
	}

	.hover-rotate:hover {
		transform: rotate(5deg);
	}

	.hover-translate-x {
		transition: transform var(--duration-normal) ease-in-out;
	}

	.hover-translate-x:hover {
		transform: translateX(4px);
	}

	/* Glass Effects */
	.glass-effect {
		background: rgba(255, 255, 255, 0.1);
		backdrop-filter: blur(10px);
		border: 1px solid rgba(255, 255, 255, 0.2);
		border-radius: var(--radius);
	}

	.glass-card {
		background: rgba(255, 255, 255, 0.1);
		backdrop-filter: blur(10px);
		border: 1px solid rgba(255, 255, 255, 0.2);
		box-shadow: var(--shadow-lg);
		border-radius: var(--radius);
	}

	/* Consistent Transitions */
	.transition-standard {
		transition: all var(--duration-normal) ease-in-out;
	}

	.transition-fast {
		transition: all var(--duration-fast) ease-in-out;
	}

	.transition-slow {
		transition: all var(--duration-slow) ease-in-out;
	}
}

/* Animation Keyframes */
@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

@keyframes slideUp {
	from {
		transform: translateY(10px);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

@keyframes slideDown {
	from {
		transform: translateY(-10px);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

@keyframes pulseSlight {
	0%,
	100% {
		opacity: 1;
	}
	50% {
		opacity: 0.9;
	}
}

@keyframes float {
	0% {
		transform: translateY(0px);
	}
	50% {
		transform: translateY(-5px);
	}
	100% {
		transform: translateY(0px);
	}
}
/* Styling for user mentions in chat */
.user-mention {
	color: #6366f1 !important; /* Indigo color for mentions */
	font-weight: bold !important;
	cursor: pointer !important;
	display: inline-block !important;
	white-space: nowrap !important;
	text-decoration: none !important;
	transition: all 0.2s ease-in-out !important;
}

.user-mention:hover {
	text-decoration: underline !important;
	opacity: 0.8 !important;
}

/* Make sure mentions are visible in the editor and renderer */
.ql-editor .user-mention,
.ql-renderer .user-mention {
	color: #6366f1 !important;
	font-weight: bold !important;
	cursor: pointer !important;
}

/* Add a special style to make mentions stand out more */
a.user-mention {
	position: relative !important;
	padding: 0 2px !important;
	border-radius: 3px !important;
	background-color: rgba(99, 102, 241, 0.1) !important;
	pointer-events: auto !important; /* Ensure clicks are captured */
	z-index: 10 !important; /* Make sure it's above other elements */
}

a.user-mention:hover {
	background-color: rgba(99, 102, 241, 0.2) !important;
}

/* Fix for Quill editor */
.ql-editor a.user-mention {
	pointer-events: auto !important;
	cursor: pointer !important;
}

/* Make sure links work in the renderer */
.ql-renderer a.user-mention {
	pointer-events: auto !important;
	cursor: pointer !important;
}

/* Styling for the mention picker */
.mention-picker {
	position: absolute;
	bottom: 100%;
	left: 0;
	right: 0;
	margin-bottom: 8px;
	z-index: 9999; /* Ensure it's above everything */
	width: 100%;
	border-radius: 0.375rem;
	border: 1px solid rgba(0, 0, 0, 0.1);
	background-color: white;
	box-shadow:
		0 4px 6px -1px rgba(0, 0, 0, 0.1),
		0 2px 4px -1px rgba(0, 0, 0, 0.06);
	overflow: hidden;
}

.mention-picker-header {
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	padding: 0.5rem;
}

.mention-picker-list {
	max-height: 200px;
	overflow-y: auto;
	padding: 0.5rem;
}

.mention-picker-search {
	border-top: 1px solid rgba(0, 0, 0, 0.1);
	padding: 0.5rem;
	background-color: rgba(0, 0, 0, 0.02);
}

/* Ensure the mention picker is visible */
.editor-container {
	position: relative;
}
/* Custom styles for Quill editor */

/* Placeholder styling */
.ql-editor.ql-blank::before {
	font-style: normal;
	font-family: 'Poppins', sans-serif;
	color: #9ca3af; /* Gray-400 */
	font-size: 0.875rem;
	font-weight: 400;
	opacity: 0.8;
}

/* Highlight the slash character for better visibility */
.ql-editor p:not(.ql-blank) span.slash-highlight {
	background-color: rgba(59, 130, 246, 0.1); /* Light blue background */
	border-radius: 2px;
	padding: 0 2px;
}

/* Styling for headings */
.ql-editor h1 {
	font-size: 1.875rem;
	font-weight: 600;
	margin-bottom: 0.75rem;
	margin-top: 1.5rem;
}

.ql-editor h2 {
	font-size: 1.5rem;
	font-weight: 600;
	margin-bottom: 0.5rem;
	margin-top: 1.25rem;
}

.ql-editor h3 {
	font-size: 1.25rem;
	font-weight: 600;
	margin-bottom: 0.5rem;
	margin-top: 1rem;
}

/* Styling for lists */
.ql-editor ul,
.ql-editor ol {
	padding-left: 1.5rem;
	margin-bottom: 0.5rem;
}

.ql-editor li {
	margin-bottom: 0.25rem;
}

/* Styling for blockquotes */
.ql-editor blockquote {
	border-left: 4px solid #e5e7eb; /* Gray-200 */
	padding-left: 1rem;
	margin-left: 0;
	margin-right: 0;
	font-style: italic;
	color: #4b5563; /* Gray-600 */
}

/* Styling for code blocks */
.ql-editor pre {
	background-color: #f3f4f6; /* Gray-100 */
	border-radius: 0.375rem;
	padding: 1rem;
	margin-bottom: 1rem;
	font-family: monospace;
	white-space: pre-wrap;
}

/* Styling for inline code */
.ql-editor code {
	background-color: #f3f4f6; /* Gray-100 */
	border-radius: 0.25rem;
	padding: 0.125rem 0.25rem;
	font-family: monospace;
}

/* Styling for images */
.ql-editor img {
	max-width: 100%;
	border-radius: 0.375rem;
	margin: 0.5rem 0;
}

/* Styling for to-do lists */
.ql-editor .todo-item {
	display: flex;
	align-items: flex-start;
	margin-bottom: 0.25rem;
}

.ql-editor .todo-checkbox {
	margin-right: 0.5rem;
	margin-top: 0.25rem;
}

/* Styling for the editor itself */
.ql-container.ql-snow {
	border: none;
	font-family: 'Poppins', sans-serif;
}

.ql-editor {
	padding: 1rem;
	min-height: 30px;
	font-size: 0.875rem;
	line-height: 1.5;
}

/* Remove default Quill border */
.ql-container.ql-snow,
.ql-toolbar.ql-snow {
	border: none;
}

.Canny_BadgeContainer .Canny_Badge {
	position: absolute;
	top: 0px;
	right: 0px;
	border-radius: 10px;
	background-color: #4a0a4f;
	padding: 5px;
	border: 1px solid white;
}
