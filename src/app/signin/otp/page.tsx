'use client';

import { OtpSignInCard } from '@/features/auth/components/otp-sign-in-card';
import { useDocumentTitle } from '@/hooks/use-document-title';

const OtpSignInPage = () => {
  // Set document title
  useDocumentTitle('Sign In with Email Code');

  return (
    <div className="flex h-full items-center justify-center bg-primary">
      <div className="md:h-auto md:w-[420px] animate-fade-in">
        <OtpSignInCard isStandalone={true} />
      </div>
    </div>
  );
};

export default OtpSignInPage;
