import { useAuthActions } from '@convex-dev/auth/react';
import { TriangleAlert, Mail, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import VerificationInput from 'react-verification-input';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

import type { SignInFlow } from '../types';

interface OtpSignInCardProps {
  setState?: (state: SignInFlow) => void;
  isStandalone?: boolean;
}

export const OtpSignInCard = ({ setState, isStandalone = false }: OtpSignInCardProps) => {
  const { signIn } = useAuthActions();
  const [step, setStep] = useState<'email' | { email: string }>('email');
  const [email, setEmail] = useState('');
  const [code, setCode] = useState('');
  const [error, setError] = useState('');
  const [pending, setPending] = useState(false);

  const handleSendCode = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    const validateEmail = (email: string) => {
      return String(email)
        .toLowerCase()
        .match(
          /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
        );
    };

    if (!validateEmail(email)) {
      setError('Please enter a valid email address.');
      return;
    }

    setPending(true);
    setError('');

    try {
      await signIn('resend-otp', { email });
      setStep({ email });
    } catch (error) {
      setError('Failed to send verification code. Please try again.');
    } finally {
      setPending(false);
    }
  };

  const handleVerifyCode = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (typeof step === 'string' || !code || code.length !== 8) {
      setError('Please enter the complete 8-digit code.');
      return;
    }

    setPending(true);
    setError('');

    try {
      await signIn('resend-otp', { email: step.email, code });
    } catch (error) {
      setError('Invalid verification code. Please try again.');
    } finally {
      setPending(false);
    }
  };

  const handleCodeComplete = (value: string) => {
    setCode(value);
    if (value.length === 8 && typeof step !== 'string') {
      // Auto-submit when code is complete
      setPending(true);
      setError('');
      
      signIn('resend-otp', { email: step.email, code: value })
        .catch(() => {
          setError('Invalid verification code. Please try again.');
        })
        .finally(() => setPending(false));
    }
  };

  const handleBackToEmail = () => {
    setStep('email');
    setCode('');
    setError('');
  };

  const handleBackToSignIn = () => {
    if (setState) {
      setState('signIn');
    }
  };

  return (
    <Card className="size-full p-8 shadow-xl border-opacity-30 backdrop-blur-sm animate-slide-up rounded-[10px]">
      <CardHeader className="px-0 pt-0">
        <CardTitle>
          {step === 'email' ? 'Sign in with Email' : 'Enter Verification Code'}
        </CardTitle>
        <CardDescription>
          {step === 'email' 
            ? 'We\'ll send you a verification code to sign in.' 
            : `We sent an 8-digit code to ${typeof step !== 'string' ? step.email : ''}`
          }
        </CardDescription>
      </CardHeader>

      {!!error && (
        <div className="mb-6 flex items-center gap-x-2 rounded-md bg-destructive/15 p-3 text-sm text-destructive">
          <TriangleAlert className="size-4" />
          <p>{error}</p>
        </div>
      )}

      <CardContent className="space-y-5 px-0 pb-0">
        {step === 'email' ? (
          <form onSubmit={handleSendCode} className="space-y-2.5">
            <Input
              disabled={pending}
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email"
              type="email"
              required
            />

            <Button 
              type="submit" 
              className="bg-primary w-full transition-standard hover:shadow-lg hover:bg-primary/90" 
              size="lg" 
              disabled={pending}
            >
              <Mail className="mr-2 size-4" />
              Send Verification Code
            </Button>
          </form>
        ) : (
          <form onSubmit={handleVerifyCode} className="space-y-4">
            <div className="flex justify-center">
              <VerificationInput
                value={code}
                onChange={setCode}
                onComplete={handleCodeComplete}
                validChars="0-9"
                length={8}
                classNames={{
                  container: cn(
                    "flex gap-x-2",
                    pending && "opacity-50 cursor-not-allowed pointer-events-none"
                  ),
                  character:
                    "h-12 w-10 rounded-md border border-gray-300 outline-primary flex items-center justify-center text-lg font-medium text-gray-700",
                  characterInactive: "bg-muted",
                  characterSelected: "bg-white text-black border-primary",
                  characterFilled: "bg-white text-black",
                }}
                autoFocus
              />
            </div>

            <div className="space-y-2">
              <Button 
                type="submit" 
                className="bg-primary w-full transition-standard hover:shadow-lg hover:bg-primary/90" 
                size="lg" 
                disabled={pending || code.length !== 8}
              >
                Verify & Sign In
              </Button>

              <Button
                type="button"
                variant="outline"
                size="lg"
                className="w-full"
                onClick={handleBackToEmail}
                disabled={pending}
              >
                <ArrowLeft className="mr-2 size-4" />
                Back to Email
              </Button>
            </div>
          </form>
        )}

        <Separator />

        <div className="flex flex-col gap-y-2.5">
          <Button
            variant="ghost"
            size="lg"
            className="w-full"
            onClick={handleBackToSignIn}
            disabled={pending}
          >
            <ArrowLeft className="mr-2 size-4" />
            Back to Sign In
          </Button>

          {!isStandalone && (
            <p className="text-xs text-muted-foreground text-center">
              Don't have an account?{' '}
              <span 
                onClick={() => setState?.('signUp')} 
                className="text-sky-700 hover:underline cursor-pointer"
              >
                Sign up
              </span>
            </p>
          )}

          {isStandalone && (
            <p className="text-xs text-muted-foreground text-center">
              Don't have an account?{' '}
              <Link href="/signup" className="text-sky-700 hover:underline">
                Sign up
              </Link>
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
