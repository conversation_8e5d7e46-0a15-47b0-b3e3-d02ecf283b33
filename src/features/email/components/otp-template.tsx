import * as React from 'react';
import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Section,
  Text,
  Hr,
  Button,
} from '@react-email/components';

interface OtpTemplateProps {
  code: string;
  email: string;
  unsubscribeUrl?: string;
  isSignUp?: boolean;
  userName?: string;
}

export const OtpTemplate: React.FC<Readonly<OtpTemplateProps>> = ({
  code,
  email,
  unsubscribeUrl,
  isSignUp = false,
  userName = '',
}) => {
  const previewText = isSignUp
    ? `Welcome to Proddy! Your verification code: ${code}`
    : `Your Proddy sign-in code: ${code}`;

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Img
            src="https://proddy.tech/logo-nobg.png"
            width="40"
            height="40"
            alt="Proddy"
            style={logo}
          />
          <Heading style={heading}>
            {isSignUp ? 'Welcome to Proddy!' : 'Sign in to Proddy'}
          </Heading>
          <Section style={section}>
            <Text style={text}>
              {isSignUp
                ? `Hi${userName ? ` ${userName}` : ''},`
                : 'Hi there,'
              }
            </Text>
            <Text style={text}>
              {isSignUp
                ? `Welcome to Proddy! You're almost ready to start collaborating with your team.`
                : `You requested to sign in to your Proddy account using ${email}.`
              }
            </Text>

            <Section style={codeContainer}>
              <Text style={codeText}>
                {code}
              </Text>
            </Section>

            <Text style={text}>
              {isSignUp
                ? 'Enter this 8-digit code in your browser to complete your account setup.'
                : 'Enter this 8-digit code in your browser to complete the sign-in process.'
              } This code will expire in 15 minutes.
            </Text>

            <Text style={text}>
              If you didn't request this code, you can safely ignore this email.
            </Text>
          </Section>

          <Hr style={hr} />
          
          <Section style={footer}>
            <Text style={footerText}>
              This email was sent to {email}. If you'd prefer not to receive these emails, you can{' '}
              {unsubscribeUrl ? (
                <a href={unsubscribeUrl} style={link}>
                  unsubscribe here
                </a>
              ) : (
                'update your email preferences in your account settings'
              )}.
            </Text>
            <Text style={footerText}>
              © 2024 Proddy. All rights reserved.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

const main = {
  backgroundColor: '#ffffff',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: '0 auto',
  padding: '20px 0 48px',
  maxWidth: '560px',
};

const logo = {
  margin: '0 auto',
  display: 'block',
};

const heading = {
  fontSize: '24px',
  letterSpacing: '-0.5px',
  lineHeight: '1.3',
  fontWeight: '400',
  color: '#484848',
  padding: '17px 0 0',
  textAlign: 'center' as const,
};

const section = {
  padding: '24px',
  border: 'solid 1px #dedede',
  borderRadius: '5px',
  textAlign: 'center' as const,
};

const text = {
  margin: '0 0 10px 0',
  textAlign: 'left' as const,
  fontSize: '14px',
  lineHeight: '1.4',
  color: '#3c4149',
};

const codeContainer = {
  background: '#f4f4f4',
  borderRadius: '4px',
  margin: '16px auto',
  padding: '24px',
  width: 'fit-content',
};

const codeText = {
  fontSize: '32px',
  lineHeight: '1.4',
  fontWeight: '600',
  color: '#484848',
  margin: '0',
  letterSpacing: '8px',
  textAlign: 'center' as const,
  fontFamily: 'monospace',
};

const hr = {
  borderColor: '#cccccc',
  margin: '20px 0',
};

const footer = {
  color: '#8898aa',
  fontSize: '12px',
  textAlign: 'center' as const,
};

const footerText = {
  margin: '0 0 10px 0',
  textAlign: 'center' as const,
  fontSize: '12px',
  lineHeight: '1.4',
  color: '#8898aa',
};

const link = {
  color: '#3869d4',
  textDecoration: 'underline',
};
